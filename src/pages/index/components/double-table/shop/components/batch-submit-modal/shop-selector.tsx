import React, { useState, useEffect, useCallback } from 'react';
import { Table, Button, Form, Input, Select, DatePicker, Space, message } from 'antd';

import { getBatchSubmitShopList } from '@/services/batch-submit';
import { IBatchSubmitShopListParams, IShopInfo, IShopFilterConditions } from '@/types/batch-submit';
import dayjs from 'dayjs';
import { traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

interface IShopSelectorProps {
  onShopsSelected: (shops: IShopInfo[]) => void;
  onCancel: () => void;
  onNext: () => void;
}

const { RangePicker } = DatePicker;

const ShopSelector: React.FC<IShopSelectorProps> = ({ onShopsSelected, onCancel, onNext }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [shopList, setShopList] = useState<IShopInfo[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedShops, setSelectedShops] = useState<IShopInfo[]>([]);
  const [pageInfo, setPageInfo] = useState<{
    pageNo: number;
    pageSize: number;
    totalCount: number;
  }>({
    pageNo: 1,
    pageSize: 100,
    totalCount: 0,
  });
  const [filters, setFilters] = useState<IShopFilterConditions>({});
  const [hasHistoricalReport, setHasHistoricalReport] = useState<boolean | undefined>(undefined);
  const [currentSort, setCurrentSort] = useState<{
    sortBy: string;
    sortType: string;
  }>({
    sortBy: 'LAST_SUBMIT_TIME',
    sortType: 'asc',
  });

  // 查询门店列表
  const fetchShopList = useCallback(
    async (params?: Partial<IBatchSubmitShopListParams>) => {
      setLoading(true);
      try {
        // 如果 params 中明确传入了筛选条件（包括 undefined），则使用 params 中的值
        // 否则使用当前状态中的 filters
        const shouldUseParamsFilters =
          params &&
          ('pid' in params ||
            'shopId' in params ||
            'hasHistoricalReport' in params ||
            'isHistoricalOrderApproved' in params ||
            'isMerchantScoreQualified' in params ||
            'lastSubmitStartTime' in params ||
            'lastSubmitEndTime' in params);

        const finalFilters = shouldUseParamsFilters
          ? {
              pid: params.pid,
              shopId: params.shopId,
              hasHistoricalReport: params.hasHistoricalReport,
              isHistoricalOrderApproved: params.isHistoricalOrderApproved,
              isMerchantScoreQualified: params.isMerchantScoreQualified,
              lastSubmitStartTime: params.lastSubmitStartTime,
              lastSubmitEndTime: params.lastSubmitEndTime,
            }
          : filters;

        const res = await getBatchSubmitShopList({
          page: params?.page || { pageNo: pageInfo.pageNo, pageSize: pageInfo.pageSize },
          source: 'BASIC_SHOP_LIST',
          sortBy: params?.sortBy || currentSort.sortBy,
          sortType: params?.sortType || currentSort.sortType,
          ...finalFilters,
          // 其他非筛选参数仍然从 params 中获取
          ...(params &&
            Object.fromEntries(
              Object.entries(params).filter(
                ([key]) =>
                  ![
                    'pid',
                    'shopId',
                    'hasHistoricalReport',
                    'isHistoricalOrderApproved',
                    'isMerchantScoreQualified',
                    'lastSubmitStartTime',
                    'lastSubmitEndTime',
                  ].includes(key),
              ),
            )),
        } as IBatchSubmitShopListParams);
        setShopList(res.dataList || []);
        setPageInfo((prev) => ({
          ...prev,
          pageNo: params?.page?.pageNo || prev.pageNo,
          pageSize: params?.page?.pageSize || prev.pageSize,
          totalCount: res.pageInfo?.totalCount || 0,
        }));
      } catch (e) {
        message.error('门店列表获取失败');
        // 重置数据避免白屏
        setShopList([]);
        setPageInfo((prev) => ({ ...prev, totalCount: 0 }));
      } finally {
        setLoading(false);
      }
    },
    [filters, pageInfo.pageNo, pageInfo.pageSize, currentSort],
  );

  useEffect(() => {
    fetchShopList();
  }, []);

  // 监听表单值变化
  const handleFormValuesChange = useCallback(
    (changedValues: any) => {
      if ('hasHistoricalReport' in changedValues) {
        setHasHistoricalReport(changedValues.hasHistoricalReport);
        if (changedValues.hasHistoricalReport === false) {
          form.setFieldsValue({
            isHistoricalOrderApproved: undefined,
            lastSubmitTime: undefined,
          });
        }
      }
    },
    [form],
  );

  // 筛选埋点
  const handleSearch = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择.筛选'], {});
    form.validateFields().then((values) => {
      const {
        pid,
        shopId,
        hasHistoricalReport: formHasHistoricalReport,
        isHistoricalOrderApproved,
        isMerchantScoreQualified,
        lastSubmitTime,
      } = values;
      const newFilters = {
        pid,
        shopId,
        hasHistoricalReport: formHasHistoricalReport,
        isHistoricalOrderApproved,
        isMerchantScoreQualified,
        lastSubmitStartTime:
          lastSubmitTime && lastSubmitTime[0]
            ? dayjs(lastSubmitTime[0]).format('YYYY-MM-DD HH:mm:ss')
            : undefined,
        lastSubmitEndTime:
          lastSubmitTime && lastSubmitTime[1]
            ? dayjs(lastSubmitTime[1]).format('YYYY-MM-DD HH:mm:ss')
            : undefined,
      };
      setFilters(newFilters);
      setPageInfo((prev) => ({ ...prev, pageNo: 1 }));
      fetchShopList({
        page: { pageNo: 1, pageSize: pageInfo.pageSize },
        ...newFilters,
      });
    });
  }, [form, pageInfo.pageSize, fetchShopList]);

  // 重置列表
  const handleReset = useCallback(() => {
    form.resetFields();
    setFilters({});
    setHasHistoricalReport(undefined);
    setPageInfo((prev) => ({ ...prev, pageNo: 1 }));
    // 直接传入空的筛选条件，避免使用异步更新的 filters 状态
    fetchShopList({
      page: { pageNo: 1, pageSize: pageInfo.pageSize },
      // 明确传入空的筛选条件，覆盖 fetchShopList 中的 filters 依赖
      pid: undefined,
      shopId: undefined,
      hasHistoricalReport: undefined,
      isHistoricalOrderApproved: undefined,
      isMerchantScoreQualified: undefined,
      lastSubmitStartTime: undefined,
      lastSubmitEndTime: undefined,
    });
  }, [form, pageInfo.pageSize, fetchShopList]);

  // 分页切换和排序处理
  const handleTableChange = useCallback(
    (pagination, _filters, sorter) => {
      // 处理排序
      if (sorter && sorter.field === 'lastSubmitTime') {
        const sortType = sorter.order === 'ascend' ? 'asc' : 'desc';
        setCurrentSort({
          sortBy: 'LAST_SUBMIT_TIME',
          sortType,
        });
        fetchShopList({
          sortBy: 'LAST_SUBMIT_TIME',
          sortType,
          page: { pageNo: pagination.current, pageSize: pagination.pageSize },
        });
      } else {
        // 保持当前排序状态
        fetchShopList({
          sortBy: currentSort.sortBy,
          sortType: currentSort.sortType,
          page: { pageNo: pagination.current, pageSize: pagination.pageSize },
        });
      }

      setPageInfo({
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
        totalCount: pageInfo.totalCount,
      });
    },
    [pageInfo.totalCount, fetchShopList, currentSort],
  );

  // 选择门店埋点
  const handleSelectChange = useCallback(
    (keys: React.Key[], rows: IShopInfo[]) => {
      traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择.选择门店'], {
        count: keys.length,
      });
      setSelectedRowKeys(keys);
      // 合并已选门店，去重
      const allSelected = [...selectedShops, ...rows].filter(
        (shop, idx, arr) => arr.findIndex((s) => s.shopId === shop.shopId) === idx,
      );
      setSelectedShops(allSelected.filter((shop) => keys.includes(shop.shopId)));
    },
    [selectedShops],
  );

  // 全选/取消全选埋点
  const handleSelectAll = useCallback(
    (selected: boolean, selectedRows: IShopInfo[], changeRows: IShopInfo[]) => {
      traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择.选择门店'], { all: selected });
      if (selected) {
        const all = [...selectedShops, ...changeRows].filter(
          (shop, idx, arr) => arr.findIndex((s) => s.shopId === shop.shopId) === idx,
        );
        setSelectedShops(all);
        setSelectedRowKeys(all.map((shop) => shop.shopId));
      } else {
        const remain = selectedShops.filter(
          (shop) => !changeRows.some((row) => row.shopId === shop.shopId),
        );
        setSelectedShops(remain);
        setSelectedRowKeys(remain.map((shop) => shop.shopId));
      }
    },
    [selectedShops],
  );

  // 下一步埋点
  const handleNext = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择.下一步'], {});
    if (selectedShops.length === 0) {
      message.warning('请至少选择一个门店');
      return;
    }
    onShopsSelected(selectedShops);
    onNext();
  }, [selectedShops, onShopsSelected, onNext]);

  // 取消埋点
  const handleCancel = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择.取消'], {});
    onCancel();
  }, [onCancel]);

  // 表格列定义
  const columns = [
    { title: '门店名称', dataIndex: 'shopName', key: 'shopName' },
    { title: '门店ID', dataIndex: 'shopId', key: 'shopId' },
    { title: '商户ID', dataIndex: 'pid', key: 'pid' },
    {
      title: '最近提报时间',
      dataIndex: 'lastSubmitTime',
      key: 'lastSubmitTime',
      sorter: true, // 启用排序
      defaultSortOrder: 'ascend' as const, // 默认升序，
    },
  ];

  return (
    <div>
      <div style={{ padding: 8, borderRadius: 6 }}>
        <Form form={form} layout="vertical" onValuesChange={handleFormValuesChange}>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 8 }}>
            <Form.Item name="pid" label="商户ID" style={{ marginBottom: 16 }}>
              <Input
                placeholder="请输入商户ID"
                allowClear
                onKeyDown={(e) => {
                  if (e.key === ' ') {
                    e.preventDefault();
                  }
                }}
              />
            </Form.Item>
            <Form.Item name="shopId" label="门店ID" style={{ marginBottom: 16 }}>
              <Input
                placeholder="请输入门店ID"
                allowClear
                onKeyDown={(e) => {
                  if (e.key === ' ') {
                    e.preventDefault();
                  }
                }}
              />
            </Form.Item>
            <Form.Item name="hasHistoricalReport" label="历史是否提报" style={{ marginBottom: 16 }}>
              <Select allowClear placeholder="请选择">
                <Select.Option value>是</Select.Option>
                <Select.Option value={false}>否</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="isHistoricalOrderApproved"
              label="历史工单是否过审"
              style={{ marginBottom: 16 }}
            >
              <Select allowClear placeholder="请选择" disabled={hasHistoricalReport === false}>
                <Select.Option value>是</Select.Option>
                <Select.Option value={false}>否</Select.Option>
              </Select>
            </Form.Item>
            {/* <Form.Item
              name="isMerchantScoreQualified"
              label="商家分是否达标"
              style={{ marginBottom: 16 }}
            >
              <Select allowClear placeholder="请选择">
                <Select.Option value>是</Select.Option>
                <Select.Option value={false}>否</Select.Option>
              </Select>
            </Form.Item> */}
            <Form.Item name="lastSubmitTime" label="最近提报时间" style={{ marginBottom: 16 }}>
              <RangePicker
                showTime
                placeholder={['开始时间', '结束时间']}
                style={{ width: '100%' }}
                disabled={hasHistoricalReport === false}
                allowEmpty={[true, true]}
              />
            </Form.Item>
          </div>
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={handleReset}>重置</Button>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
            </Space>
          </div>
        </Form>
      </div>
      <Table
        rowKey="shopId"
        loading={loading}
        columns={columns}
        dataSource={shopList}
        pagination={{
          current: pageInfo.pageNo,
          pageSize: pageInfo.pageSize,
          defaultPageSize: 100,
          total: pageInfo.totalCount,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showQuickJumper: {
            goButton: (
              <Button size="small" type="primary" style={{ marginLeft: 8 }}>
                确定
              </Button>
            ),
          },
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: handleSelectChange,
          onSelectAll: handleSelectAll,
          getCheckboxProps: (record: IShopInfo) => ({
            disabled: record.canSelect === false,
          }),
        }}
        onChange={handleTableChange}
        scroll={{ y: 400 }}
      />
      <div
        style={{
          marginTop: 24,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <div style={{ fontSize: '14px', color: '#666' }}>
          涉及{' '}
          <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
            {new Set(selectedShops.map((shop) => shop.pid)).size}
          </span>{' '}
          个商户，
          <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{selectedShops.length}</span>{' '}
          个门店
        </div>
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleNext}>
            下一步
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default ShopSelector;
